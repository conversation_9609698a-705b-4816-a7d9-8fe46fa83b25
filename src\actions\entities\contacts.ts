"use server";

import { z } from "zod";
import { auth } from "@/lib/auth";
import { db } from "@/lib/prisma";
import { ContactSchema } from "@/schemas/zod";
import { revalidatePath } from "next/cache";
import { ContactType } from "@prisma/client";

/**
 * Get effective user ID (owner ID if employee, user's own ID otherwise)
 */
async function getEffectiveUserId() {
  const session = await auth();
  const user = session?.user;

  if (!user || !user.id) {
    return null;
  }

  return user.id;
}

/**
 * Create a new unified contact
 */
export const createContact = async (
  values: z.infer<typeof ContactSchema>,
  bankAccounts?: Array<{
    bankName: string;
    bankBranch: string;
    accountHolder: string;
    accountNumber: string;
  }>
) => {
  // Get effective user ID (owner ID if employee, user's own ID otherwise)
  const effectiveUserId = await getEffectiveUserId();

  if (!effectiveUserId) {
    return { error: "Tidak terautentikasi!" };
  }
  const userId = effectiveUserId;

  // 1. Validate input server-side
  const validatedFields = ContactSchema.safeParse(values);

  if (!validatedFields.success) {
    console.error(
      "Validation Error:",
      validatedFields.error.flatten().fieldErrors
    );
    return { error: "Input tidak valid!" };
  }

  const {
    displayName,
    contactGroup,
    firstName,
    middleName,
    lastName,
    phone,
    telephone,
    fax,
    email,
    identityType,
    identityNumber,
    NIK,
    NPWP,
    companyName,
    otherInfo,
    billingAddress,
    shippingAddress,
    sameAsShipping,
    bankName,
    bankBranch,
    accountHolder,
    accountNumber,
    notes,
  } = validatedFields.data;

  try {
    // Convert contactGroup to ContactType enum
    let contactType: ContactType;
    switch (contactGroup) {
      case "customer":
        contactType = ContactType.CUSTOMER;
        break;
      case "supplier":
        contactType = ContactType.SUPPLIER;
        break;
      case "employee":
        contactType = ContactType.EMPLOYEE;
        break;
      default:
        return { error: "Grup kontak tidak valid!" };
    }

    // 2. Create the contact in the database with transaction for multiple bank accounts
    // If email is empty string, set it to null to avoid unique constraint issues
    const contact = await db.$transaction(async (tx) => {
      // Create the main contact
      const newContact = await tx.contact.create({
        data: {
          displayName,
          contactGroup: contactType,
          firstName,
          middleName,
          lastName,
          phone,
          telephone,
          fax,
          email: email === "" ? null : email, // Convert empty string to null
          identityType,
          identityNumber,
          NIK,
          NPWP,
          companyName,
          otherInfo,
          billingAddress,
          shippingAddress: sameAsShipping ? billingAddress : shippingAddress,
          sameAsShipping,
          // Keep backward compatibility fields for first bank account
          bankName: bankAccounts?.[0]?.bankName || bankName,
          bankBranch: bankAccounts?.[0]?.bankBranch || bankBranch,
          accountHolder: bankAccounts?.[0]?.accountHolder || accountHolder,
          accountNumber: bankAccounts?.[0]?.accountNumber || accountNumber,
          notes,
          userId, // Associate with the current user
        },
      });

      // Create multiple bank accounts if provided
      if (bankAccounts && bankAccounts.length > 0) {
        // Filter out empty bank accounts
        const validBankAccounts = bankAccounts.filter(
          (account) =>
            account.bankName.trim() &&
            account.accountHolder.trim() &&
            account.accountNumber.trim()
        );

        if (validBankAccounts.length > 0) {
          await tx.contactBankAccount.createMany({
            data: validBankAccounts.map((account) => ({
              contactId: newContact.id,
              bankName: account.bankName,
              bankBranch: account.bankBranch || null,
              accountHolder: account.accountHolder,
              accountNumber: account.accountNumber,
            })),
          });
        }
      }

      return newContact;
    });

    // 3. Revalidate the contacts page to show the new contact
    revalidatePath("/dashboard/contacts");
    revalidatePath("/dashboard/contacts/new");

    return {
      success: "Kontak berhasil ditambahkan!",
      contact,
      redirectTo: `/dashboard/contacts?tab=${contactGroup === "customer" ? "pelanggan" : "supplier"}`,
    };
  } catch (error) {
    console.error("Error creating contact:", error);
    return { error: "Gagal menambahkan kontak. Silakan coba lagi." };
  }
};

/**
 * Get all contacts for the current user
 */
export const getContacts = async () => {
  const effectiveUserId = await getEffectiveUserId();

  if (!effectiveUserId) {
    return { error: "Tidak terautentikasi!" };
  }

  try {
    const contacts = await db.contact.findMany({
      where: {
        userId: effectiveUserId,
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    return { contacts };
  } catch (error) {
    console.error("Error fetching contacts:", error);
    return { error: "Gagal mengambil data kontak." };
  }
};

/**
 * Get a specific contact by ID
 */
export const getContactById = async (id: string) => {
  const effectiveUserId = await getEffectiveUserId();

  if (!effectiveUserId) {
    return { error: "Tidak terautentikasi!" };
  }

  try {
    const contact = await db.contact.findFirst({
      where: {
        id,
        userId: effectiveUserId,
      },
    });

    if (!contact) {
      return { error: "Kontak tidak ditemukan!" };
    }

    return { contact };
  } catch (error) {
    console.error("Error fetching contact:", error);
    return { error: "Gagal mengambil data kontak." };
  }
};

/**
 * Update a contact
 */
export const updateContact = async (
  id: string,
  values: z.infer<typeof ContactSchema>
) => {
  const effectiveUserId = await getEffectiveUserId();

  if (!effectiveUserId) {
    return { error: "Tidak terautentikasi!" };
  }
  const userId = effectiveUserId;

  // 1. Validate input server-side
  const validatedFields = ContactSchema.safeParse(values);

  if (!validatedFields.success) {
    console.error(
      "Validation Error:",
      validatedFields.error.flatten().fieldErrors
    );
    return { error: "Input tidak valid!" };
  }

  const {
    displayName,
    contactGroup,
    firstName,
    middleName,
    lastName,
    phone,
    telephone,
    fax,
    email,
    identityType,
    identityNumber,
    NIK,
    NPWP,
    companyName,
    otherInfo,
    billingAddress,
    shippingAddress,
    sameAsShipping,
    bankName,
    bankBranch,
    accountHolder,
    accountNumber,
    notes,
  } = validatedFields.data;

  try {
    // Convert contactGroup to ContactType enum
    let contactType: ContactType;
    switch (contactGroup) {
      case "customer":
        contactType = ContactType.CUSTOMER;
        break;
      case "supplier":
        contactType = ContactType.SUPPLIER;
        break;
      case "employee":
        contactType = ContactType.EMPLOYEE;
        break;
      default:
        return { error: "Grup kontak tidak valid!" };
    }

    // 2. Update the contact in the database
    const contact = await db.contact.update({
      where: {
        id,
        userId, // Ensure the contact belongs to the current user
      },
      data: {
        displayName,
        contactGroup: contactType,
        firstName,
        middleName,
        lastName,
        phone,
        telephone,
        fax,
        email: email === "" ? null : email, // Convert empty string to null
        identityType,
        identityNumber,
        NIK,
        NPWP,
        companyName,
        otherInfo,
        billingAddress,
        shippingAddress: sameAsShipping ? billingAddress : shippingAddress,
        sameAsShipping,
        bankName,
        bankBranch,
        accountHolder,
        accountNumber,
        notes,
      },
    });

    // 3. Revalidate the contacts page to show the updated contact
    revalidatePath("/dashboard/contacts");
    revalidatePath(`/dashboard/contacts/${id}`);

    return { success: "Kontak berhasil diperbarui!", contact };
  } catch (error) {
    console.error("Error updating contact:", error);
    return { error: "Gagal memperbarui kontak. Silakan coba lagi." };
  }
};

/**
 * Delete a contact
 */
export const deleteContact = async (id: string) => {
  const effectiveUserId = await getEffectiveUserId();

  if (!effectiveUserId) {
    return { error: "Tidak terautentikasi!" };
  }

  try {
    await db.contact.delete({
      where: {
        id,
        userId: effectiveUserId, // Ensure the contact belongs to the current user
      },
    });

    // Revalidate the contacts page
    revalidatePath("/dashboard/contacts");

    return { success: "Kontak berhasil dihapus!" };
  } catch (error) {
    console.error("Error deleting contact:", error);
    return { error: "Gagal menghapus kontak. Silakan coba lagi." };
  }
};
