"use client";

import React, { useState } from "react";
import { useRouter } from "next/navigation";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { toast } from "sonner";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { UserCog, Save, Loader2 } from "lucide-react";
import { Role } from "@prisma/client";

// Employee form schema
const employeeSchema = z.object({
  name: z.string().min(1, "Nama karyawan wajib diisi"),
  employeeId: z.string().min(1, "ID karyawan wajib diisi"),
  role: z.enum(["ADMIN", "CASHIER"], {
    required_error: "Role karyawan wajib dipilih",
  }),
  password: z.string().min(6, "Password minimal 6 karakter"),
  confirmPassword: z.string().min(6, "Konfirmasi password minimal 6 karakter"),
}).refine((data) => data.password === data.confirmPassword, {
  message: "Password dan konfirmasi password tidak cocok",
  path: ["confirmPassword"],
});

type EmployeeFormData = z.infer<typeof employeeSchema>;

const EmployeeForm: React.FC = () => {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    watch,
  } = useForm<EmployeeFormData>({
    resolver: zodResolver(employeeSchema),
  });

  const selectedRole = watch("role");

  const onSubmit = async (data: EmployeeFormData) => {
    setIsLoading(true);
    try {
      // Remove confirmPassword from the data before sending
      const { confirmPassword, ...employeeData } = data;
      
      const response = await fetch("/api/employees", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(employeeData),
      });

      if (response.ok) {
        toast.success("Karyawan berhasil ditambahkan!");
        router.push("/dashboard/contacts?tab=karyawan");
      } else {
        const errorData = await response.json();
        toast.error(errorData.message || "Gagal menambahkan karyawan");
      }
    } catch (error) {
      console.error("Error creating employee:", error);
      toast.error("Terjadi kesalahan saat menambahkan karyawan");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <UserCog className="h-5 w-5 text-purple-600" />
          Informasi Karyawan
        </CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Employee Name */}
            <div className="space-y-2">
              <Label htmlFor="name">
                Nama Karyawan <span className="text-red-500">*</span>
              </Label>
              <Input
                id="name"
                {...register("name")}
                placeholder="Masukkan nama lengkap karyawan"
                className={errors.name ? "border-red-500" : ""}
              />
              {errors.name && (
                <p className="text-sm text-red-500">{errors.name.message}</p>
              )}
            </div>

            {/* Employee ID */}
            <div className="space-y-2">
              <Label htmlFor="employeeId">
                ID Karyawan <span className="text-red-500">*</span>
              </Label>
              <Input
                id="employeeId"
                {...register("employeeId")}
                placeholder="Masukkan ID unik karyawan"
                className={errors.employeeId ? "border-red-500" : ""}
              />
              {errors.employeeId && (
                <p className="text-sm text-red-500">{errors.employeeId.message}</p>
              )}
            </div>

            {/* Role */}
            <div className="space-y-2">
              <Label htmlFor="role">
                Role <span className="text-red-500">*</span>
              </Label>
              <Select onValueChange={(value) => setValue("role", value as "ADMIN" | "CASHIER")}>
                <SelectTrigger className={errors.role ? "border-red-500" : ""}>
                  <SelectValue placeholder="Pilih role karyawan" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="ADMIN">Admin</SelectItem>
                  <SelectItem value="CASHIER">Kasir</SelectItem>
                </SelectContent>
              </Select>
              {errors.role && (
                <p className="text-sm text-red-500">{errors.role.message}</p>
              )}
            </div>

            {/* Role Description */}
            <div className="space-y-2">
              <Label>Deskripsi Role</Label>
              <div className="p-3 bg-gray-50 dark:bg-gray-800 rounded-md text-sm">
                {selectedRole === "ADMIN" && (
                  <p className="text-purple-600 dark:text-purple-400">
                    Admin memiliki akses penuh kecuali manajemen karyawan
                  </p>
                )}
                {selectedRole === "CASHIER" && (
                  <p className="text-blue-600 dark:text-blue-400">
                    Kasir memiliki akses terbatas untuk penjualan dan produk
                  </p>
                )}
                {!selectedRole && (
                  <p className="text-gray-500">Pilih role untuk melihat deskripsi</p>
                )}
              </div>
            </div>

            {/* Password */}
            <div className="space-y-2">
              <Label htmlFor="password">
                Password <span className="text-red-500">*</span>
              </Label>
              <Input
                id="password"
                type="password"
                {...register("password")}
                placeholder="Masukkan password (min. 6 karakter)"
                className={errors.password ? "border-red-500" : ""}
              />
              {errors.password && (
                <p className="text-sm text-red-500">{errors.password.message}</p>
              )}
            </div>

            {/* Confirm Password */}
            <div className="space-y-2">
              <Label htmlFor="confirmPassword">
                Konfirmasi Password <span className="text-red-500">*</span>
              </Label>
              <Input
                id="confirmPassword"
                type="password"
                {...register("confirmPassword")}
                placeholder="Ulangi password"
                className={errors.confirmPassword ? "border-red-500" : ""}
              />
              {errors.confirmPassword && (
                <p className="text-sm text-red-500">{errors.confirmPassword.message}</p>
              )}
            </div>
          </div>

          {/* Submit Button */}
          <div className="flex justify-end space-x-4">
            <Button
              type="button"
              variant="outline"
              onClick={() => router.back()}
              disabled={isLoading}
            >
              Batal
            </Button>
            <Button
              type="submit"
              disabled={isLoading}
              className="bg-purple-600 hover:bg-purple-700"
            >
              {isLoading ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Menyimpan...
                </>
              ) : (
                <>
                  <Save className="h-4 w-4 mr-2" />
                  Simpan Karyawan
                </>
              )}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
};

export default EmployeeForm;
