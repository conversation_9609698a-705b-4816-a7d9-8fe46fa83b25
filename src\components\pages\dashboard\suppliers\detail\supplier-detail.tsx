"use client";

import React from "react";
import Link from "next/link";
import { Supplier as PrismaSupplier } from "@prisma/client";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  ArrowLeft,
  Edit,
  Building2,
  Mail,
  Phone,
  MapPin,
  Calendar,
  FileText,
  User,
} from "lucide-react";
import { format } from "date-fns";
import { id } from "date-fns/locale";

interface SupplierDetailPageProps {
  supplier: PrismaSupplier;
}

const SupplierDetailPage: React.FC<SupplierDetailPageProps> = ({ supplier }) => {
  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Link href="/dashboard/contacts?tab=supplier">
              <Button
                variant="ghost"
                size="sm"
                className="flex items-center gap-2"
              >
                <ArrowLeft className="h-4 w-4" />
                Kembali
              </Button>
            </Link>
            <div>
              <h1 className="text-2xl font-bold text-gray-900 dark:text-white flex items-center gap-3">
                <Building2 className="h-6 w-6 text-green-600 dark:text-green-400" />
                Detail Supplier
              </h1>
              <p className="text-gray-600 dark:text-gray-400 mt-1">
                Informasi lengkap supplier
              </p>
            </div>
          </div>
          <Link href={`/dashboard/suppliers/${supplier.id}/edit`}>
            <Button className="flex items-center gap-2">
              <Edit className="h-4 w-4" />
              Edit
            </Button>
          </Link>
        </div>
      </div>

      {/* Supplier Information */}
      <div className="max-w-4xl mx-auto px-6">
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden">
          {/* Supplier Header */}
          <div className="bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <div className="flex items-center gap-4">
              <div className="w-12 h-12 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center">
                <Building2 className="h-6 w-6 text-green-600 dark:text-green-400" />
              </div>
              <div>
                <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
                  {supplier.name}
                </h2>
                {supplier.contactName && (
                  <p className="text-gray-600 dark:text-gray-400">
                    Kontak: {supplier.contactName}
                  </p>
                )}
              </div>
            </div>
          </div>

          {/* Supplier Details */}
          <div className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Contact Information */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium text-gray-900 dark:text-white border-b border-gray-200 dark:border-gray-700 pb-2">
                  Informasi Kontak
                </h3>
                
                <div className="space-y-3">
                  <div className="flex items-center gap-3">
                    <User className="h-4 w-4 text-gray-400" />
                    <span className="text-sm text-gray-600 dark:text-gray-400 w-24">Nama Kontak:</span>
                    <span className="text-sm text-gray-900 dark:text-white">
                      {supplier.contactName || "-"}
                    </span>
                  </div>
                  
                  <div className="flex items-center gap-3">
                    <Mail className="h-4 w-4 text-gray-400" />
                    <span className="text-sm text-gray-600 dark:text-gray-400 w-24">Email:</span>
                    <span className="text-sm text-gray-900 dark:text-white">
                      {supplier.email || "-"}
                    </span>
                  </div>
                  
                  <div className="flex items-center gap-3">
                    <Phone className="h-4 w-4 text-gray-400" />
                    <span className="text-sm text-gray-600 dark:text-gray-400 w-24">Telepon:</span>
                    <span className="text-sm text-gray-900 dark:text-white">
                      {supplier.phone || "-"}
                    </span>
                  </div>
                  
                  <div className="flex items-start gap-3">
                    <MapPin className="h-4 w-4 text-gray-400 mt-0.5" />
                    <span className="text-sm text-gray-600 dark:text-gray-400 w-24">Alamat:</span>
                    <span className="text-sm text-gray-900 dark:text-white">
                      {supplier.address || "-"}
                    </span>
                  </div>
                </div>
              </div>

              {/* Additional Information */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium text-gray-900 dark:text-white border-b border-gray-200 dark:border-gray-700 pb-2">
                  Informasi Tambahan
                </h3>
                
                <div className="space-y-3">
                  <div className="flex items-start gap-3">
                    <FileText className="h-4 w-4 text-gray-400 mt-0.5" />
                    <span className="text-sm text-gray-600 dark:text-gray-400 w-24">Catatan:</span>
                    <span className="text-sm text-gray-900 dark:text-white">
                      {supplier.notes || "-"}
                    </span>
                  </div>
                  
                  <div className="flex items-center gap-3">
                    <Calendar className="h-4 w-4 text-gray-400" />
                    <span className="text-sm text-gray-600 dark:text-gray-400 w-24">Dibuat:</span>
                    <span className="text-sm text-gray-900 dark:text-white">
                      {format(new Date(supplier.createdAt), "dd MMMM yyyy 'pukul' HH:mm", { locale: id })}
                    </span>
                  </div>
                  
                  <div className="flex items-center gap-3">
                    <Calendar className="h-4 w-4 text-gray-400" />
                    <span className="text-sm text-gray-600 dark:text-gray-400 w-24">Diperbarui:</span>
                    <span className="text-sm text-gray-900 dark:text-white">
                      {format(new Date(supplier.updatedAt), "dd MMMM yyyy 'pukul' HH:mm", { locale: id })}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SupplierDetailPage;
