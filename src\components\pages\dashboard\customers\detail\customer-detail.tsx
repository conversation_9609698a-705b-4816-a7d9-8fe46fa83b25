"use client";

import React from "react";
import Link from "next/link";
import { Customer as PrismaCustomer } from "@prisma/client";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  ArrowLeft,
  Edit,
  User,
  Mail,
  Phone,
  MapPin,
  Calendar,
  FileText,
  CreditCard,
  UserCheck,
} from "lucide-react";
import { format } from "date-fns";
import { id } from "date-fns/locale";

interface CustomerDetailPageProps {
  customer: PrismaCustomer;
}

const CustomerDetailPage: React.FC<CustomerDetailPageProps> = ({ customer }) => {
  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Link href="/dashboard/contacts?tab=pelanggan">
              <Button
                variant="ghost"
                size="sm"
                className="flex items-center gap-2"
              >
                <ArrowLeft className="h-4 w-4" />
                Kembali
              </Button>
            </Link>
            <div>
              <h1 className="text-2xl font-bold text-gray-900 dark:text-white flex items-center gap-3">
                <User className="h-6 w-6 text-blue-600 dark:text-blue-400" />
                Detail Pelanggan
              </h1>
              <p className="text-gray-600 dark:text-gray-400 mt-1">
                Informasi lengkap pelanggan
              </p>
            </div>
          </div>
          <Link href={`/dashboard/customers/${customer.id}/edit`}>
            <Button className="flex items-center gap-2">
              <Edit className="h-4 w-4" />
              Edit
            </Button>
          </Link>
        </div>
      </div>

      {/* Customer Information */}
      <div className="max-w-4xl mx-auto px-6">
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden">
          {/* Customer Header */}
          <div className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <div className="flex items-center gap-4">
              <div className="w-12 h-12 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center">
                <User className="h-6 w-6 text-blue-600 dark:text-blue-400" />
              </div>
              <div>
                <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
                  {customer.name}
                </h2>
                {customer.contactName && (
                  <p className="text-gray-600 dark:text-gray-400">
                    Kontak: {customer.contactName}
                  </p>
                )}
              </div>
            </div>
          </div>

          {/* Customer Details */}
          <div className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Contact Information */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium text-gray-900 dark:text-white border-b border-gray-200 dark:border-gray-700 pb-2">
                  Informasi Kontak
                </h3>
                
                <div className="space-y-3">
                  <div className="flex items-center gap-3">
                    <Mail className="h-4 w-4 text-gray-400" />
                    <span className="text-sm text-gray-600 dark:text-gray-400 w-20">Email:</span>
                    <span className="text-sm text-gray-900 dark:text-white">
                      {customer.email || "-"}
                    </span>
                  </div>
                  
                  <div className="flex items-center gap-3">
                    <Phone className="h-4 w-4 text-gray-400" />
                    <span className="text-sm text-gray-600 dark:text-gray-400 w-20">Telepon:</span>
                    <span className="text-sm text-gray-900 dark:text-white">
                      {customer.phone || "-"}
                    </span>
                  </div>
                  
                  <div className="flex items-start gap-3">
                    <MapPin className="h-4 w-4 text-gray-400 mt-0.5" />
                    <span className="text-sm text-gray-600 dark:text-gray-400 w-20">Alamat:</span>
                    <span className="text-sm text-gray-900 dark:text-white">
                      {customer.address || "-"}
                    </span>
                  </div>
                </div>
              </div>

              {/* Identity Information */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium text-gray-900 dark:text-white border-b border-gray-200 dark:border-gray-700 pb-2">
                  Informasi Identitas
                </h3>
                
                <div className="space-y-3">
                  <div className="flex items-center gap-3">
                    <UserCheck className="h-4 w-4 text-gray-400" />
                    <span className="text-sm text-gray-600 dark:text-gray-400 w-20">NIK:</span>
                    <span className="text-sm text-gray-900 dark:text-white">
                      {customer.NIK || "-"}
                    </span>
                  </div>
                  
                  <div className="flex items-center gap-3">
                    <CreditCard className="h-4 w-4 text-gray-400" />
                    <span className="text-sm text-gray-600 dark:text-gray-400 w-20">NPWP:</span>
                    <span className="text-sm text-gray-900 dark:text-white">
                      {customer.NPWP || "-"}
                    </span>
                  </div>
                </div>
              </div>

              {/* Additional Information */}
              <div className="space-y-4 md:col-span-2">
                <h3 className="text-lg font-medium text-gray-900 dark:text-white border-b border-gray-200 dark:border-gray-700 pb-2">
                  Informasi Tambahan
                </h3>
                
                <div className="space-y-3">
                  <div className="flex items-start gap-3">
                    <FileText className="h-4 w-4 text-gray-400 mt-0.5" />
                    <span className="text-sm text-gray-600 dark:text-gray-400 w-20">Catatan:</span>
                    <span className="text-sm text-gray-900 dark:text-white">
                      {customer.notes || "-"}
                    </span>
                  </div>
                  
                  <div className="flex items-center gap-3">
                    <Calendar className="h-4 w-4 text-gray-400" />
                    <span className="text-sm text-gray-600 dark:text-gray-400 w-20">Dibuat:</span>
                    <span className="text-sm text-gray-900 dark:text-white">
                      {format(new Date(customer.createdAt), "dd MMMM yyyy 'pukul' HH:mm", { locale: id })}
                    </span>
                  </div>
                  
                  <div className="flex items-center gap-3">
                    <Calendar className="h-4 w-4 text-gray-400" />
                    <span className="text-sm text-gray-600 dark:text-gray-400 w-20">Diperbarui:</span>
                    <span className="text-sm text-gray-900 dark:text-white">
                      {format(new Date(customer.updatedAt), "dd MMMM yyyy 'pukul' HH:mm", { locale: id })}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CustomerDetailPage;
